{"expo": {"name": "DailyPlayground", "slug": "DailyPlayground", "version": "1.0.0", "newArchEnabled": true, "icon": "./assets/icon.png", "ios": {"infoPlist": {"UIBackgroundModes": ["voip"]}, "bitcode": false, "bundleIdentifier": "co.daily.DailyPlayground"}, "plugins": [["@daily-co/config-plugin-rn-daily-js", {"enableCamera": true, "enableMicrophone": true, "enableScreenShare": true}], ["expo-build-properties", {"android": {"minSdkVersion": 24}, "ios": {"deploymentTarget": "15.1"}}]], "android": {"package": "co.daily.DailyPlayground"}}}