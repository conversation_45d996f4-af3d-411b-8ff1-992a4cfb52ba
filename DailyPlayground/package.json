{"name": "dailyplayground", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start --dev-client", "android": "expo run:android --device", "ios": "expo run:ios --device", "web": "expo start --web"}, "dependencies": {"@daily-co/config-plugin-rn-daily-js": "0.0.9", "@daily-co/react-native-daily-js": "^0.80.0", "@daily-co/react-native-webrtc": "118.0.3-daily.4", "@react-native-async-storage/async-storage": "^1.24.0", "@react-native-clipboard/clipboard": "^1.16.2", "expo": "~53.0.9", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-native": "^0.79.2", "react-native-background-timer": "^2.4.1", "react-native-dropdown-picker": "^5.4.6", "react-native-get-random-values": "^1.11.0", "react-native-svg": "^15.12.0"}, "devDependencies": {"@babel/core": "^7.27.3", "@types/react": "~19.0.14", "typescript": "^5.8.3"}, "private": true}